<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Contract Type Change Confirmation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input {
            width: 200px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>اختبار تأكيد تغيير نوع العقد</h1>
    
    <div class="info">
        <h3>تعليمات الاختبار:</h3>
        <ol>
            <li>اختر "تحديث عقد موجود" لمحاكاة تحديث عقد</li>
            <li>اختر نوع عقد مختلف عن النوع الأصلي</li>
            <li>اضغط "حفظ العقد" لرؤية رسالة التأكيد</li>
        </ol>
    </div>

    <form id="testForm" onsubmit="return validateForm()">
        <div class="form-group">
            <label>وضع العقد:</label>
            <button type="button" onclick="simulateNewContract()">عقد جديد</button>
            <button type="button" onclick="simulateUpdateContract()">تحديث عقد موجود</button>
        </div>

        <div class="form-group">
            <label for="contract_id">معرف العقد:</label>
            <input type="text" id="contract_id" value="0" readonly>
        </div>

        <div class="form-group">
            <label for="contract_type">نوع العقد:</label>
            <select id="contract_type" name="contract_type">
                <option value="">اختر نوع العقد</option>
                <option value="1">شهري مفتوح</option>
                <option value="2">شهري مغلق</option>
                <option value="3">يومي مفتوح</option>
                <option value="4">يومي مغلق</option>
            </select>
        </div>

        <div class="form-group">
            <label>النوع الأصلي للعقد:</label>
            <span id="originalTypeDisplay">غير محدد</span>
        </div>

        <button type="submit">حفظ العقد</button>
    </form>

    <script>
        // Variable to store original contract type for updates
        let originalContractType = null;

        // Simulate new contract creation
        function simulateNewContract() {
            document.getElementById('contract_id').value = '0';
            document.getElementById('contract_type').value = '';
            originalContractType = null;
            document.getElementById('originalTypeDisplay').textContent = 'غير محدد';
            alert('تم تعيين وضع "عقد جديد"');
        }

        // Simulate updating existing contract
        function simulateUpdateContract() {
            document.getElementById('contract_id').value = '123';
            document.getElementById('contract_type').value = '1'; // Set to monthly open
            originalContractType = '1'; // Store original as monthly open
            document.getElementById('originalTypeDisplay').textContent = 'شهري مفتوح (1)';
            alert('تم تعيين وضع "تحديث عقد موجود" - النوع الأصلي: شهري مفتوح');
        }

        // Form validation function (copied from the main implementation)
        function validateForm() {
            const contractId = document.getElementById('contract_id').value;
            const isUpdate = contractId > 0;
            
            // Check if contract type has changed for updates
            if (isUpdate && originalContractType !== null) {
                const currentContractType = document.getElementById('contract_type').value;
                if (originalContractType !== currentContractType) {
                    const confirmMessage = 'تحذير: تم تغيير نوع العقد!\n\n' +
                                         'سيؤدي هذا التغيير إلى حذف جميع تقارير الإنجاز المرتبطة بهذا العقد (إن وجدت) ويجب إعادة إنشاؤها.\n\n' +
                                         'هل تريد المتابعة؟';
                    
                    if (!confirm(confirmMessage)) {
                        return false; // User cancelled the operation
                    }
                }
            }
            
            // For testing purposes, prevent actual form submission
            alert('تم التحقق من النموذج بنجاح! (في التطبيق الحقيقي سيتم حفظ العقد)');
            return false; // Prevent actual submission for testing
        }

        // Update display when contract type changes
        document.getElementById('contract_type').addEventListener('change', function() {
            const typeNames = {
                '1': 'شهري مفتوح',
                '2': 'شهري مغلق', 
                '3': 'يومي مفتوح',
                '4': 'يومي مغلق'
            };
            
            const currentType = this.value;
            const isUpdate = document.getElementById('contract_id').value > 0;
            
            if (isUpdate && originalContractType && currentType !== originalContractType) {
                this.style.backgroundColor = '#fff3cd'; // Yellow background to indicate change
                this.style.borderColor = '#ffeaa7';
            } else {
                this.style.backgroundColor = '';
                this.style.borderColor = '#ccc';
            }
        });
    </script>
</body>
</html>
