# Contract Type Change Implementation

## Overview
This implementation ensures that when a contract's type is changed (e.g., from daily open/closed to monthly open/closed, or vice versa), all associated achievement_reports records are automatically deleted.

## Implementation Details

### Modified File
- **File**: `Pages\create_contract_con.php`
- **Lines Modified**: 450-578 (approximately)

### Changes Made

1. **Added Contract Type Check Logic**:
   - Before updating a contract, the system now retrieves the current contract_type
   - Compares the current contract_type with the new contract_type
   - If they differ, deletes all related achievement_reports records

2. **User Confirmation Dialog**:
   - JavaScript validation checks if contract type has changed during updates
   - Shows confirmation dialog warning about achievement_reports deletion
   - User must click "OK" to proceed with the operation
   - Operation is cancelled if user clicks "Cancel"

3. **Database Operations Added**:
   ```sql
   -- Get current contract type
   SELECT contract_type FROM contract WHERE id_contract = ?

   -- Delete related achievement reports if type changed
   DELETE FROM achievement_reports WHERE id_contract = ?

   -- Update contract (existing logic)
   UPDATE contract SET ... WHERE id_contract = ?
   ```

4. **Enhanced User Feedback**:
   - Success message now indicates when achievement_reports were deleted due to contract type change
   - Logging added for debugging purposes

### Code Flow

1. **Contract Loading**: When updating an existing contract, original contract_type is stored in JavaScript variable
2. **User Interaction**: User modifies contract type in the form
3. **Form Submission**: User clicks "Update" button
4. **Client-Side Validation**: JavaScript checks if contract type has changed
5. **Confirmation Dialog**: If type changed, show warning dialog about achievement_reports deletion
6. **User Decision**: User must confirm to proceed or cancel the operation
7. **Server-Side Processing**: If confirmed:
   - System fetches current contract_type from database
   - Compares current type with new type from form
   - If types differ, deletes all achievement_reports records with matching id_contract
   - Logs the deletion for audit purposes
8. **Contract Update**: Proceed with normal contract update
9. **User Notification**: Display appropriate success message

### Contract Types
The system uses the following contract type values:
- `1`: Monthly contract
- `2`: Daily contract

The frontend may use different values (1-4) but they are converted to 1 or 2 in the backend:
- Frontend values 1-2 → Backend value 1 (Monthly)
- Frontend values 3-4 → Backend value 2 (Daily)

### JavaScript Implementation Details

**Variables Added**:
- `originalContractType`: Stores the initial contract type when loading an existing contract

**Functions Modified**:
- `validateForm()`: Enhanced to check for contract type changes and show confirmation dialog
- Contract loading logic: Stores original contract type when populating form for updates
- Form reset logic: Clears original contract type when switching to new contract mode

**Confirmation Dialog**:
- Appears only when updating existing contracts and contract type has changed
- Uses native JavaScript `confirm()` function for maximum compatibility
- Returns `false` to prevent form submission if user cancels

### Error Handling
- Database connection errors are caught and displayed
- SQL preparation and execution errors are handled gracefully
- Transaction rollback is not implemented as the operations are separate
- JavaScript validation prevents form submission if user cancels confirmation

### Testing
Two test files have been created to verify the functionality:

1. **`test_contract_type_change.php`** - Server-side testing:
   - Tests contract type change scenarios
   - Verifies achievement_reports deletion
   - Confirms no deletion when type remains the same

2. **`test_contract_type_confirmation.html`** - Client-side testing:
   - Tests the JavaScript confirmation dialog
   - Simulates contract update scenarios
   - Verifies user interaction flow

## Database Schema Reference

### achievement_reports Table
```sql
CREATE TABLE achievement_reports (
    id_achievement_reports INT AUTO_INCREMENT PRIMARY KEY,
    id_Project INT,
    id_contract INT,
    id_extension_contract INT,
    id_permanent_diapers INT,
    start_date_achievement_reports DATETIME,
    end_date_achievement_reports DATETIME,
    data_todo_list_achievement JSON,
    actual_working_days INT,
    add_achievement_reports TIMESTAMP
);
```

### contract Table (relevant fields)
```sql
CREATE TABLE contract (
    id_contract INT AUTO_INCREMENT PRIMARY KEY,
    contract_type INT,
    -- other fields...
);
```

## Usage
This functionality is triggered when:
1. User accesses the contract update form (`Pages\create_contract_con.php`)
2. Selects an existing contract for update
3. Changes the contract type
4. Clicks the "Update" button
5. **Confirmation Dialog**: A warning message appears stating:
   ```
   تحذير: تم تغيير نوع العقد!

   سيؤدي هذا التغيير إلى حذف جميع تقارير الإنجاز المرتبطة بهذا العقد (إن وجدت) ويجب إعادة إنشاؤها.

   هل تريد المتابعة؟
   ```
6. User must click "OK" to proceed or "Cancel" to abort the operation
7. If confirmed, the deletion and update proceed automatically

## Security Considerations
- Uses prepared statements to prevent SQL injection
- Validates contract existence before proceeding
- Logs operations for audit trail
- Proper error handling prevents information disclosure

## Future Enhancements
Consider implementing:
1. Backup of deleted achievement_reports before deletion
2. User confirmation dialog when contract type change will delete reports
3. Transaction wrapping for atomic operations
4. More detailed logging with timestamps and user information
