# Contract Type Change Implementation

## Overview
This implementation ensures that when a contract's type is changed (e.g., from daily open/closed to monthly open/closed, or vice versa), all associated achievement_reports records are automatically deleted.

## Implementation Details

### Modified File
- **File**: `Pages\create_contract_con.php`
- **Lines Modified**: 450-578 (approximately)

### Changes Made

1. **Added Contract Type Check Logic**:
   - Before updating a contract, the system now retrieves the current contract_type
   - Compares the current contract_type with the new contract_type
   - If they differ, deletes all related achievement_reports records

2. **Database Operations Added**:
   ```sql
   -- Get current contract type
   SELECT contract_type FROM contract WHERE id_contract = ?
   
   -- Delete related achievement reports if type changed
   DELETE FROM achievement_reports WHERE id_contract = ?
   
   -- Update contract (existing logic)
   UPDATE contract SET ... WHERE id_contract = ?
   ```

3. **Enhanced User Feedback**:
   - Success message now indicates when achievement_reports were deleted due to contract type change
   - Logging added for debugging purposes

### Code Flow

1. **Contract Update Request**: User submits contract update form
2. **Current Type Retrieval**: System fetches current contract_type from database
3. **Type Comparison**: Compare current type with new type from form
4. **Conditional Deletion**: If types differ:
   - Delete all achievement_reports records with matching id_contract
   - Log the deletion for audit purposes
5. **Contract Update**: Proceed with normal contract update
6. **User Notification**: Display appropriate success message

### Contract Types
The system uses the following contract type values:
- `1`: Monthly contract
- `2`: Daily contract

The frontend may use different values (1-4) but they are converted to 1 or 2 in the backend:
- Frontend values 1-2 → Backend value 1 (Monthly)
- Frontend values 3-4 → Backend value 2 (Daily)

### Error Handling
- Database connection errors are caught and displayed
- SQL preparation and execution errors are handled gracefully
- Transaction rollback is not implemented as the operations are separate

### Testing
A test script `test_contract_type_change.php` has been created to verify the functionality:
- Tests contract type change scenarios
- Verifies achievement_reports deletion
- Confirms no deletion when type remains the same

## Database Schema Reference

### achievement_reports Table
```sql
CREATE TABLE achievement_reports (
    id_achievement_reports INT AUTO_INCREMENT PRIMARY KEY,
    id_Project INT,
    id_contract INT,
    id_extension_contract INT,
    id_permanent_diapers INT,
    start_date_achievement_reports DATETIME,
    end_date_achievement_reports DATETIME,
    data_todo_list_achievement JSON,
    actual_working_days INT,
    add_achievement_reports TIMESTAMP
);
```

### contract Table (relevant fields)
```sql
CREATE TABLE contract (
    id_contract INT AUTO_INCREMENT PRIMARY KEY,
    contract_type INT,
    -- other fields...
);
```

## Usage
This functionality is automatically triggered when:
1. User accesses the contract update form (`Pages\create_contract_con.php`)
2. Selects an existing contract for update
3. Changes the contract type
4. Submits the form

No additional user action is required - the deletion happens automatically and transparently.

## Security Considerations
- Uses prepared statements to prevent SQL injection
- Validates contract existence before proceeding
- Logs operations for audit trail
- Proper error handling prevents information disclosure

## Future Enhancements
Consider implementing:
1. Backup of deleted achievement_reports before deletion
2. User confirmation dialog when contract type change will delete reports
3. Transaction wrapping for atomic operations
4. More detailed logging with timestamps and user information
