<?php
/**
 * Test script to verify that achievement_reports are deleted when contract type changes
 * This script simulates the contract type change functionality
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database connection
$file = fopen(__DIR__ . "/Pages/connection/one.txt", "r");
if (!$file) {
    die('Error reading configuration file');
}

$servername = trim(fgets($file));
$username = trim(fgets($file));
$password = trim(fgets($file));
$dbname = trim(fgets($file));
fclose($file);

$conn = new mysqli($servername, $username, $password, $dbname);
$conn->set_charset("utf8");

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "<h2>Contract Type Change Test</h2>\n";

// Function to test contract type change logic
function testContractTypeChange($conn, $contract_id, $new_contract_type) {
    echo "<h3>Testing Contract ID: $contract_id</h3>\n";
    
    // Get current contract type
    $current_contract_sql = "SELECT contract_type FROM contract WHERE id_contract = ?";
    $current_stmt = $conn->prepare($current_contract_sql);
    if (!$current_stmt) {
        echo "Error preparing current contract query: " . $conn->error . "\n";
        return false;
    }
    
    $current_stmt->bind_param("i", $contract_id);
    $current_stmt->execute();
    $current_result = $current_stmt->get_result();
    $current_contract = $current_result->fetch_assoc();
    $current_stmt->close();
    
    if (!$current_contract) {
        echo "Contract not found!\n";
        return false;
    }
    
    $current_contract_type = $current_contract['contract_type'];
    echo "Current contract type: $current_contract_type\n";
    echo "New contract type: $new_contract_type\n";
    
    // Count achievement reports before
    $count_sql = "SELECT COUNT(*) as count FROM achievement_reports WHERE id_contract = ?";
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param("i", $contract_id);
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $before_count = $count_result->fetch_assoc()['count'];
    $count_stmt->close();
    
    echo "Achievement reports before: $before_count\n";
    
    // Check if contract type is changing
    if ($current_contract_type != $new_contract_type) {
        echo "Contract type is changing - achievement reports should be deleted\n";
        
        // Delete achievement reports (simulating the logic from create_contract_con.php)
        $delete_reports_sql = "DELETE FROM achievement_reports WHERE id_contract = ?";
        $delete_stmt = $conn->prepare($delete_reports_sql);
        if (!$delete_stmt) {
            echo "Error preparing delete query: " . $conn->error . "\n";
            return false;
        }
        
        $delete_stmt->bind_param("i", $contract_id);
        if (!$delete_stmt->execute()) {
            echo "Error deleting achievement reports: " . $delete_stmt->error . "\n";
            return false;
        }
        
        $deleted_count = $delete_stmt->affected_rows;
        $delete_stmt->close();
        
        echo "Deleted $deleted_count achievement reports\n";
        
        // Update contract type (simulating the update)
        $update_sql = "UPDATE contract SET contract_type = ? WHERE id_contract = ?";
        $update_stmt = $conn->prepare($update_sql);
        $update_stmt->bind_param("ii", $new_contract_type, $contract_id);
        
        if ($update_stmt->execute()) {
            echo "Contract type updated successfully\n";
        } else {
            echo "Error updating contract type: " . $update_stmt->error . "\n";
        }
        $update_stmt->close();
        
    } else {
        echo "Contract type is not changing - no deletion needed\n";
    }
    
    // Count achievement reports after
    $count_stmt = $conn->prepare($count_sql);
    $count_stmt->bind_param("i", $contract_id);
    $count_stmt->execute();
    $count_result = $count_stmt->get_result();
    $after_count = $count_result->fetch_assoc()['count'];
    $count_stmt->close();
    
    echo "Achievement reports after: $after_count\n";
    echo "---\n";
    
    return true;
}

// Get some sample contracts to test with
$sample_contracts_sql = "SELECT id_contract, contract_type FROM contract LIMIT 5";
$result = $conn->query($sample_contracts_sql);

if ($result->num_rows > 0) {
    echo "<h3>Available Contracts for Testing:</h3>\n";
    while ($row = $result->fetch_assoc()) {
        echo "Contract ID: " . $row['id_contract'] . ", Current Type: " . $row['contract_type'] . "\n";
    }
    echo "\n";
    
    // Test with first contract - change type from current to different
    $result->data_seek(0);
    $first_contract = $result->fetch_assoc();
    $contract_id = $first_contract['id_contract'];
    $current_type = $first_contract['contract_type'];
    $new_type = ($current_type == 1) ? 2 : 1; // Switch between 1 and 2
    
    echo "<h3>Running Test:</h3>\n";
    testContractTypeChange($conn, $contract_id, $new_type);
    
    // Test with same type (should not delete)
    echo "<h3>Testing with same type (should not delete):</h3>\n";
    testContractTypeChange($conn, $contract_id, $new_type); // Same as current now
    
} else {
    echo "No contracts found in database\n";
}

$conn->close();
?>
